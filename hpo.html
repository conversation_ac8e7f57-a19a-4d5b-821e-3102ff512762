<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Professional Orthopedic Practice Landing Page</title>
  <link rel="stylesheet" href="https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/themes/df-messenger-default.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Syncopate:wght@400;700&family=DM+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/df-messenger.js"></script>
<style>
        body {
            font-family: 'Syncopate', sans-serif;
            background: #ffffff;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        /* Enhanced Dialogflow Messenger Styling */
        /* https://cloud.google.com/dialogflow/cx/docs/concept/integration/dialogflow-messenger/css */
        df-messenger {
            z-index: 999;
            position: fixed;
            bottom: 20px;
            left: 20px;
            top: auto;
            right: auto;
            margin: 0 !important;
            --df-messenger-titlebar-title-font-family: 'Syncopate', sans-serif;
            --df-messenger-titlebar-title-font-size: 16px;
            --df-messenger-font-family: 'DM Sans', sans-serif;
            --df-messenger-font-color: #0e2c50;
            --df-messenger-font-size: 14px;
            /* --df-messenger-chat-border: 1px solid #0e2c50; */
            --df-messenger-chat-background: #ffffff;
            --df-messenger-message-user-background: #1d4376;
            --df-messenger-message-bot-background: #f8f9fa;
            --df-messenger-message-bot-font-color: #0e2c50;
            --df-messenger-message-user-font-color: #ffffff;
            --df-messenger-chat-border-radius: 20px;
            --df-messenger-chat-bubble-background: #0091ce;
            --df-messenger-chat-bubble-font-color: #ffffff;
            --df-messenger-input-background: #f8f9fa;
            --df-messenger-input-font-color: #0e2c50;
            --df-messenger-input-border-color: #0091ce;
            --df-messenger-input-placeholder-font-color: #1d4376;
            --df-messenger-titlebar-background: #1d4376;
            --df-messenger-titlebar-font-color: #ffffff;
            --df-messenger-titlebar-title-font-weight: 600;
            --df-messenger-titlebar-icon-font-color: #ffffff;
            --df-messenger-chat-overflow: auto;
            --df-messenger-chat-bubble-icon-color: #ffffff;
            --df-messenger-chat-window-offset: 16px;
        }
    </style>
    <script type="module" crossorigin src="hpo.js"></script>
    <link rel="stylesheet" type="text/css" crossorigin href="hpo.css">
  </head>
  <body>
    <div id="root"></div>

<df-messenger
  project-id="frontdesk-454309"
  agent-id="7773dff3-6832-4fd6-9011-a49280fb4436"
  language-code="en"
  max-query-length="-1">
  <df-messenger-chat-bubble
    chat-title="Hudson Pro Ortho">
  </df-messenger-chat-bubble>
</df-messenger>

  </body>
</html>
