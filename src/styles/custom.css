/* Custom styles for FrontDesk AI */

.bg-gradient-purple-blue {
  background: linear-gradient(180deg, #5A359A 0%, #0F1BA0 100%);
}

.wave-animation {
  animation: wave 1.5s infinite;
}

@keyframes wave {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.2); }
}

.arrow-warmup {
  display: inline-block;
  position: relative;
  top: -4px;
  width: 47px;
}

.hero-rounded {
  border-radius: 40px;
}

.button-rounded {
  border-radius: 100px;
}

.message-box-rounded {
  border-radius: 20px;
}

.arrow-forward {
  height: 24px;
  width: 24px;
}

.bg-blue-gradient {
  background: linear-gradient(90deg, #0015FF 0%, rgba(0, 21, 255, 0.5) 100%);
}

.pagination-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  display: inline-block;
  margin: 0 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-dot.active {
  width: 32px;
  height: 6px;
  border-radius: 3px;
  background-color: #FFFFFF;
  animation: expandDot 0.3s ease-out;
}

@keyframes expandDot {
  from { width: 8px; height: 8px; border-radius: 50%; }
  to { width: 32px; height: 6px; border-radius: 3px; }
}

.pagination-dot:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

.chat-shadow {
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.25);
}

.feature-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #55FF00;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  font-family: 'Mulish', sans-serif;
}

.logo-ai-sufix {
  position: relative;
  top: -8px;
  right: -4px;
}

.text-underline {
  text-decoration: underline !important;
}

.no-spacing {
  margin-top: -1px; /* Removes any gap between sections */
}

/* Animated text styles */
.animated-text {
  display: inline-block;
  opacity: 0;
  animation: fadeIn 0.1s forwards;
  animation-play-state: paused;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Progress bar styles */
.progress-container {
  width: 100%;
  height: 3px;
  background-color: rgba(205, 194, 194, 0.1);
  border-radius: 10px;
  margin-top: 12px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  width: 0%;
  background-color: #ffffff;
  border-radius: 10px;
  transition: width 0.1s linear;
  position: absolute;
  top: 0;
  left: 0;
}

/* Mobile Menu Styles */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  cursor: pointer;
  z-index: 30;
  background: transparent;
  border: none;
  padding: 0;
}

.hamburger-line {
  display: block;
  width: 100%;
  height: 3px;
  background-color: white;
  border-radius: 10px;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.mobile-menu {
  transition: all 0.3s ease-in-out;
  background: linear-gradient(180deg, #5A359A 0%, #0F1BA0 100%);
}

.mobile-nav-link {
  color: white;
  font-family: 'Mulish', sans-serif;
  font-size: 24px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
}

.mobile-nav-link:hover {
  transform: scale(1.05);
}

.mobile-menu-close {
  cursor: pointer;
  background: transparent;
  border: none;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.mobile-menu-close:hover {
  transform: scale(1.1);
}

/* Media Queries for Responsive Design */
@media screen and (max-width: 1024px) {
  .hero-heading {
    font-size: 50px;
  }
  
  .w-\[450px\] {
    width: 400px;
  }
}

@media screen and (max-width: 768px) {
  .desktop-nav, 
  .desktop-cta {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .hero-heading {
    font-size: 40px;
  }
  
  .w-\[450px\] {
    width: 100%;
    max-width: 350px;
  }
  
  .mt-40 {
    margin-top: 90px;
  }
  
  .p-8 {
    padding: 20px;
  }
  
  .pb-\[300px\] {
    padding-bottom: 180px;
  }
  
  .hero-rounded {
    border-radius: 30px;
    height: calc(100vh - 20px);
  }
  
  /* Message bubble taller on mobile */
  .message-bubble {
    height: 110px !important;
  }
  
  /* Hide decorative elements on mobile */
  .absolute.left-0.top-\[120px\] {
    display: none;
  }
  
  .absolute.top-20.right-\[-80px\] {
    display: none;
  }
  
  .absolute.w-\[330px\].left-\[150px\].top-\[70px\] {
    display: none;
  }
  
  .arrow-warmup {
    width: 35px;
  }
}

@media screen and (max-width: 480px) {
  .hero-heading {
    font-size: 32px;
  }
  
  .w-\[450px\] {
    width: 100%;
    max-width: 280px;
  }
  
  .p-8 {
    padding: 15px;
  }
  
  .pb-\[300px\] {
    padding-bottom: 120px;
  }
  
  .hero-rounded {
    border-radius: 20px;
  }
  
  .p-4 {
    padding: 10px;
  }
  
  .absolute.bottom-10.right-6 {
    bottom: 5px;
    right: 5px;
  }
  
  .w-\[56px\], 
  .h-\[56px\] {
    width: 42px;
    height: 42px;
  }
}

/* FrontDesk Solutions Section Styles */
.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.feature-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.chat-message-container {
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.25);
  border-radius: 20px;
  overflow: hidden;
  width: 95%;
  max-width: 450px;
  margin: 0 auto;
  position: absolute;
  top: 0;
  left: calc(50% + 20px);
  transform: translateX(-50%);
  opacity: 1;
  transition: transform 0.6s ease, opacity 0.6s ease, z-index 0s 0.3s;
}

/* Media queries for the solutions section */
@media screen and (max-width: 1024px) {
  .solutions-heading {
    font-size: 40px;
    margin-bottom: 2rem;
  }
  
  .feature-heading {
    font-size: 26px;
  }
}

@media screen and (max-width: 768px) {
  .solutions-heading {
    font-size: 36px;
    margin-bottom: 1.5rem;
  }
  
  .feature-heading {
    font-size: 24px;
  }
  
  .solutions-container {
    padding: 30px 20px;
  }
}

@media screen and (max-width: 480px) {
  .solutions-heading {
    font-size: 30px;
    margin-bottom: 1.5rem;
  }
  
  .feature-heading {
    font-size: 22px;
  }
  
  .solutions-container {
    padding: 20px 15px;
  }
  
  .feature-item {
    margin-bottom: 10px;
  }
  
  .chat-message-container {
    max-width: 100%;
  }
}

/* Chat Carousel Styles */
.chat-carousel {
  position: relative;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  overflow: visible;
  height: 220px;
  padding-top: 20px;
}

.carousel-item,
.prev-slide,
.next-slide {
  position: absolute;
  top: 0;
  left: calc(50% + 20px);
  margin-bottom: 1rem;
  transition: all 0.6s ease;
}

.carousel-item.active {
  opacity: 1;
  transform: translateX(-50%) scale(1);
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.25);
  z-index: 5;
}

.prev-slide {
  transform: translate(calc(-50% - 40px), -40px);
  z-index: 3;
}

.next-slide {
  transform: translate(calc(-50% - 20px), -20px);
  z-index: 4;
}

/* Update pagination dots to be lines when active */
.pagination-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  display: inline-block;
  margin: 0 5px;
  cursor: pointer;
  transition: all 0.6s ease;
}

.pagination-dot.active {
  width: 32px;
  height: 6px;
  border-radius: 3px;
  background-color: #FFFFFF;
  animation: expandDot 0.6s ease-out;
}

@keyframes expandDot {
  from { width: 8px; height: 8px; border-radius: 50%; }
  to { width: 32px; height: 6px; border-radius: 3px; }
}

.pagination-dot:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

/* Calendar Integration Section Styles */
.calendar-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  padding: 2rem;
}

.calendar-image-container {
  width: 100%;
  height: 100%;
  position: relative;
  bottom: -50px;
  right: -60px;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}

/* Calendar appearance */
.calendar-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.calendar-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.calendar-month {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.calendar-tabs {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.calendar-tab {
  padding: 0.25rem 0.5rem;
  cursor: pointer;
}

.calendar-tab.active {
  background-color: #f5f5f5;
  border-radius: 9999px;
}

.calendar-dates {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  text-align: center;
}

.calendar-date {
  padding: 0.5rem;
}

.calendar-date-number {
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.calendar-date-day {
  font-size: 0.75rem;
  color: #666;
}

.calendar-appointments {
  position: relative;
  height: 120px;
  margin-bottom: 1rem;
}

.calendar-times {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 30px;
}

.calendar-time {
  font-size: 0.75rem;
  color: #666;
  text-align: right;
  padding-right: 0.5rem;
}

.calendar-columns {
  position: absolute;
  left: 40px;
  right: 0;
  top: 0;
  bottom: 0;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.5rem;
}

.calendar-column {
  position: relative;
  height: 100%;
}

.appointment {
  position: absolute;
  left: 0;
  right: 0;
  border-radius: 0.25rem;
  padding: 0.25rem;
  font-size: 0.75rem;
  line-height: 1.2;
}

.appointment-orange {
  background-color: #FFEDD5;
}

.appointment-red {
  background-color: #FEE2E2;
}

.appointment-blue {
  background-color: #DBEAFE;
}

.appointment-yellow {
  background-color: #e8e3c6;
}

.appointment-label {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 0.125rem;
  padding: 0 0.25rem;
  display: inline-block;
  font-size: 0.7rem;
}

.calendar-progress {
  margin-top: 1rem;
  padding-top: 1.5rem;
  position: relative;
}

.progress-bar {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 2px;
  background-color: #ffffff;
  border-radius: 9999px;
}

.progress-label {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 0.75rem;
  color: #666;
}

/* Media queries for the calendar section */
@media screen and (max-width: 1024px) {
  .calendar-heading {
    font-size: 28px;
  }
  
  .calendar-container {
    padding: 1.5rem;
  }
  
  .calendar-appointments {
    height: 100px;
  }
}

@media screen and (max-width: 768px) {
  .calendar-heading {
    font-size: 26px;
  }
  
  .calendar-container {
    padding: 1.25rem;
  }
  
  .calendar-image-container {
    padding: 0.75rem;
    bottom: 0;
    right: 0;
  }
  
  .calendar-times {
    width: 25px;
  }
  
  .calendar-columns {
    left: 30px;
  }
  
  .calendar-appointments {
    height: 90px;
  }

  .appointment,
  .appointment-label {
    font-size: 0.5rem;
  }
}

@media screen and (max-width: 480px) {
  .calendar-heading {
    font-size: 24px;
  }
  
  .calendar-container {
    padding: 1rem;
  }
  
  .calendar-image-container {
    padding: 0.5rem;
  }
  
  .calendar-tabs {
    display: none;
  }
  
  .calendar-times {
    width: 20px;
  }
  
  .calendar-columns {
    left: 25px;
  }
  
  .calendar-appointments {
    height: 80px;
  }
}

/* Dialogflow Messenger custom theme */
df-messenger {
  --df-messenger-font-family: 'Mulish', sans-serif;
  --df-messenger-message-user-background: #0015FF; /* royal blue */
  --df-messenger-message-bot-background: #5A359A; /* purple */
  --df-messenger-message-user-font-color: #FFFFFF;
  --df-messenger-message-bot-font-color: #FFFFFF;
  --df-messenger-chat-border-radius: 20px;
  --df-messenger-button-titlebar-border-radius: 20px 20px 0 0;
}
